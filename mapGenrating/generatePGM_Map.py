import cv2
from pyproj import Proj, Transformer
import numpy as np
import os

def get_utm_zone(lon, lat):
    """
    Determine the UTM zone number for given longitude and latitude
    """
    # UTM zones are 6 degrees wide, starting at -180
    zone_number = int((lon + 180) / 6) + 1
    
    # Handle special cases for Norway and Svalbard
    if lat >= 56.0 and lat < 64.0 and lon >= 3.0 and lon < 12.0:
        zone_number = 32
    elif lat >= 72.0 and lat < 84.0:
        if lon >= 0.0 and lon < 9.0:
            zone_number = 31
        elif lon >= 9.0 and lon < 21.0:
            zone_number = 33
        elif lon >= 21.0 and lon < 33.0:
            zone_number = 35
        elif lon >= 33.0 and lon < 42.0:
            zone_number = 37
    
    # Determine if we're in the northern or southern hemisphere
    zone_letter = 'N' if lat >= 0 else 'S'
    
    return zone_number, zone_letter

def validate_coordinates(coordinates):
    """
    Validate coordinates to ensure they can be used to generate a map
    Returns: (is_valid, error_message)
    """
    if not coordinates:
        return False, "No coordinates provided"
    
    if len(coordinates) < 3:
        return False, f"Not enough coordinates: {len(coordinates)} (need at least 3)"
    
    # Check if coordinates have lat/lng keys
    for i, point in enumerate(coordinates):
        if not isinstance(point, dict):
            return False, f"Coordinate {i} is not a dictionary: {point}"
        
        if 'lat' not in point or 'lng' not in point:
            return False, f"Coordinate {i} is missing lat/lng keys: {point}"
        
        if not isinstance(point['lat'], (int, float)) or not isinstance(point['lng'], (int, float)):
            return False, f"Coordinate {i} has invalid lat/lng values: {point}"
    
    return True, ""

def generate_map(coordinates, output_path="map.pgm", resolution=0.05, progress_callback=None):
    """
    Generate a PGM map from a list of coordinates
    coordinates: list of dicts with 'lat' and 'lng' keys
    output_path: path to save the generated map
    resolution: map resolution in meters per pixel
    progress_callback: optional callback function for progress updates (value, message)
    """
    if progress_callback:
        progress_callback(5, "Starting map generation...")
    
    print(f"Starting map generation with {len(coordinates)} coordinates")
    print(f"Output path: {output_path}")
    print(f"Resolution: {resolution} meters/pixel")
    
    # Validate coordinates
    is_valid, error_message = validate_coordinates(coordinates)
    if not is_valid:
        print(f"Error: {error_message}")
        return None
    
    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
    
    if progress_callback:
        progress_callback(10, "Calculating map dimensions...")
    
    # Calculate the center point of the coordinates
    center_lon = sum(point["lng"] for point in coordinates) / len(coordinates)
    center_lat = sum(point["lat"] for point in coordinates) / len(coordinates)
    
    # Get the appropriate UTM zone
    zone_number, zone_letter = get_utm_zone(center_lon, center_lat)
    print(f"Using UTM Zone {zone_number}{zone_letter}")
    
    # Create UTM projection for the detected zone
    utm_proj = Proj(proj="utm", zone=zone_number, ellps="WGS84", south=(zone_letter == 'S'))
    
    if progress_callback:
        progress_callback(20, "Converting coordinates to UTM...")
    
    # Convert to UTM (meters) - optimize by using numpy vectorization
    lngs = np.array([point["lng"] for point in coordinates])
    lats = np.array([point["lat"] for point in coordinates])
    x, y = utm_proj(lngs, lats)
    utm_points = np.column_stack((x, y))

    # Calculate map bounds
    min_x, min_y = np.min(utm_points, axis=0)
    max_x, max_y = np.max(utm_points, axis=0)
    print("Min", min_x, min_y)
    print("Max", max_x, max_y)

    if progress_callback:
        progress_callback(30, "Calculating map size...")
    
    # Map parameters
    width_m = max_x - min_x
    height_m = max_y - min_y
    
    # Check if the map would be too large and adjust resolution if needed
    max_dimension = 4000  # Maximum dimension in pixels (reduced for better performance)
    width_px = int(width_m / resolution)
    height_px = int(height_m / resolution)
    
    if width_px > max_dimension or height_px > max_dimension:
        # Calculate new resolution to fit within max_dimension
        new_resolution = max(width_m / max_dimension, height_m / max_dimension)
        print(f"WARNING: Map too large ({width_px}x{height_px}), adjusting resolution from {resolution} to {new_resolution}")
        resolution = new_resolution
        width_px = int(width_m / resolution)
        height_px = int(height_m / resolution)
    
    print(f"Map dimensions: {width_m}m x {height_m}m ({width_px}px x {height_px}px)")

    if progress_callback:
        progress_callback(40, f"Creating map ({width_px}x{height_px} pixels)...")
    
    # Create map layers - use uint8 for memory efficiency
    free_space_layer = np.zeros((height_px, width_px), dtype=np.uint8)
    border_layer = np.zeros((height_px, width_px), dtype=np.uint8)

    if progress_callback:
        progress_callback(50, "Converting coordinates to pixels...")
    
    # Convert UTM to pixel coordinates
    pixel_points = [(
        int((x - min_x) / resolution),
        height_px - int((y - min_y) / resolution)  # Flip Y-axis
    ) for (x, y) in utm_points]
    pixel_points = np.array([pixel_points], dtype=np.int32)

    if progress_callback:
        progress_callback(60, "Drawing map interior...")
    
    # Draw filled polygon (interior, 255=free)
    cv2.fillPoly(free_space_layer, pixel_points, color=255)

    if progress_callback:
        progress_callback(70, "Drawing map borders...")
    
    # Draw border (temporary 255, thickness=50cm)
    border_thickness = max(1, int(0.5 / resolution))  # 50cm thick border, minimum 1 pixel
    cv2.polylines(border_layer, pixel_points, isClosed=True, 
                  color=255, thickness=border_thickness)

    if progress_callback:
        progress_callback(80, "Creating final map...")
    
    # Create final map (default=unknown, 205)
    grid = np.full((height_px, width_px), 205, dtype=np.uint8)
    grid[free_space_layer == 255] = 255  # Interior=free (255)
    grid[border_layer == 255] = 0        # Border=occupied (0)

    if progress_callback:
        progress_callback(90, "Saving map files...")
    
    # Save PGM
    print(f"Saving map to {output_path}")
    cv2.imwrite(output_path, grid)
    
    # Also save to the Gazebo maps directory if it exists
    gazebo_map_path = "/home/<USER>/asv_ws/src/asv_wave_sim/asv_wave_sim_gazebo/maps/map.pgm"
    try:
        gazebo_dir = os.path.dirname(gazebo_map_path)
        if os.path.exists(gazebo_dir):
            cv2.imwrite(gazebo_map_path, grid)
            print(f"Also saved to Gazebo maps directory: {gazebo_map_path}")
    except Exception as e:
        print(f"Warning: Could not save to Gazebo maps directory: {str(e)}")
    
    # Also save a PNG version for easier viewing
    png_path = output_path.replace('.pgm', '.png')
    cv2.imwrite(png_path, grid)
    print(f"Also saved PNG version to {png_path}")
    
    if progress_callback:
        progress_callback(100, "Map generation complete!")
    
    return output_path

if __name__ == "__main__":
    # Example usage
    from map_data import MAPS_DATA
    coordinates = MAPS_DATA["Paris Area 1"]
    generate_map(coordinates)
