import os
import json
import glob

# Firebase imports (optional)
try:
    import firebase_admin
    from firebase_admin import credentials, db

    cred = credentials.Certificate("/home/<USER>/navigation/navigation_system-main(1)/navigation_system-main/pathPlannig/auth.json")
    firebase_admin.initialize_app(cred, {
        'databaseURL': 'https://boatt-217dd-default-rtdb.firebaseio.com/'
    })
    FIREBASE_AVAILABLE = True
except Exception as e:
    print(f"Firebase not available: {e}")
    FIREBASE_AVAILABLE = False

def get_maps_from_firebase():
    if not FIREBASE_AVAILABLE:
        return None
    ref = db.reference('navigation/Maps')
    return ref.get()

def load_maps_from_json_files():
    """
    Load maps from JSON files in Downloads folder and maps_json directory
    Returns a dictionary with map names as keys and coordinates as values
    """
    maps_data = {}

    # Check Downloads folder
    downloads_folder = os.path.join(os.path.expanduser('~'), 'Downloads')

    # Check maps_json directory
    maps_json_dir = os.path.join(os.path.dirname(__file__), 'maps_json')

    # Search in both directories
    search_dirs = [downloads_folder, maps_json_dir]

    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            json_files = glob.glob(os.path.join(search_dir, "*.json"))

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # Check if this is a map file (contains coordinates)
                    for key, value in data.items():
                        if isinstance(value, list) and len(value) > 0:
                            # Verify it contains coordinate objects
                            if isinstance(value[0], dict) and 'lat' in value[0] and 'lng' in value[0]:
                                maps_data[key] = value
                                print(f"Loaded map '{key}' from {json_file}")

                except Exception as e:
                    print(f"Error reading {json_file}: {str(e)}")

    return maps_data

def get_map_file_path(map_name):
    """
    Find the JSON file path that contains the specified map
    Returns the file path if found, None otherwise
    """
    # Check Downloads folder first
    downloads_folder = os.path.join(os.path.expanduser('~'), 'Downloads')

    # Check maps_json directory
    maps_json_dir = os.path.join(os.path.dirname(__file__), 'maps_json')

    # Search in both directories
    search_dirs = [downloads_folder, maps_json_dir]

    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            json_files = glob.glob(os.path.join(search_dir, "*.json"))

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    if map_name in data:
                        # Verify it contains valid coordinates
                        coords = data[map_name]
                        if isinstance(coords, list) and len(coords) > 0:
                            if isinstance(coords[0], dict) and 'lat' in coords[0] and 'lng' in coords[0]:
                                return json_file

                except Exception as e:
                    print(f"Error reading {json_file}: {str(e)}")

    return None

def fetch_and_format_maps_from_firebase():
    """Fetch maps from Firebase and format them to match MAPS_DATA structure"""
    if not FIREBASE_AVAILABLE:
        return {}

    raw_maps = get_maps_from_firebase()
    formatted_maps = {}
    if not raw_maps:
        return formatted_maps

    for map_id, map_obj in raw_maps.items():
        name = map_obj.get("name", f"Map_{map_id}")
        coords = []
        for coord in map_obj.get("coordinates", []):
            if isinstance(coord, dict):
                coords.append({
                    "lat": coord.get("lat"),
                    "lng": coord.get("long") if "long" in coord else coord.get("lng")
                })
        formatted_maps[name] = coords
    return formatted_maps

# Initialize MAPS_DATA with JSON files (priority) and Firebase (fallback)
MAPS_DATA = {}

# Load maps from JSON files first
json_maps = load_maps_from_json_files()
MAPS_DATA.update(json_maps)

# Add Firebase maps as fallback
if FIREBASE_AVAILABLE:
    try:
        firebase_maps = fetch_and_format_maps_from_firebase()
        for map_name, coords in firebase_maps.items():
            if map_name not in MAPS_DATA:
                MAPS_DATA[map_name] = coords
    except Exception as e:
        print(f"Warning: Could not load maps from Firebase: {str(e)}")
def get_available_maps(refresh=False):
    """
    Get list of available map names
    If refresh=True, reload maps from JSON files
    """
    global MAPS_DATA

    if refresh:
        # Reload maps from JSON files
        MAPS_DATA = {}
        json_maps = load_maps_from_json_files()
        MAPS_DATA.update(json_maps)

        # Add Firebase maps as fallback
        if FIREBASE_AVAILABLE:
            try:
                firebase_maps = fetch_and_format_maps_from_firebase()
                for map_name, coords in firebase_maps.items():
                    if map_name not in MAPS_DATA:
                        MAPS_DATA[map_name] = coords
            except Exception as e:
                print(f"Warning: Could not load maps from Firebase: {str(e)}")

    return list(MAPS_DATA.keys())

def get_map_coordinates(map_name):
    """Get coordinates for a specific map"""
    return MAPS_DATA.get(map_name, [])
