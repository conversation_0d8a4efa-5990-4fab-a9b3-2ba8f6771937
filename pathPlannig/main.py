#!/usr/bin/env python3

import numpy as np
import cv2
from PyQt5.QtWidgets import (QApplication, QMainWindow, QGraphicsView,
                            QGraphicsScene, QVBoxLayout,
                            QWidget, QPushButton, QLabel, QHBoxLayout, QComboBox,
                            QMessageBox, QLineEdit, QGridLayout, QProgressDialog)
from PyQt5.QtGui import QPixmap, QImage, QPen, QColor, QWheelEvent, QPainter
from PyQt5.QtCore import Qt, QPointF, QFileSystemWatcher, QTimer, QThread, pyqtSignal
import math
import os
import json
import sys  # Import manquant
from datetime import datetime
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from mapGenrating.map_data import get_available_maps, get_map_file_path
from mapGenrating.generatePGM_Map import generate_map, validate_coordinates
import subprocess
import glob
import time

# Firebase configuration
import firebase_admin
from firebase_admin import credentials, db

def initialize_firebase():
    # Check if Firebase app is already initialized
    if not firebase_admin._apps:
        try:
            cred = credentials.Certificate("auth.json")
            firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://boatt-217dd-default-rtdb.firebaseio.com/'
            })
            return True
        except Exception as e:
            print(f"Error initializing Firebase: {str(e)}")
            return False
    return True

def upload_waypoints_to_firebase(waypoints_data):
    try:
        if initialize_firebase():
            ref = db.reference('navigation/coverage_path_planning')
            ref.push(waypoints_data)
            return True
        return False
    except Exception as e:
        print(f"Error uploading to Firebase: {str(e)}")
        return False

def check_user_credentials(username, password):
    """Check user credentials against Firebase database"""
    try:
        if not initialize_firebase():
            return False, None
            
        ref = db.reference('users')
        users = ref.get()
        
        if not users:
            return False, None
            
        for _, user_data in users.items():
            if user_data.get('username') == username and user_data.get('password') == password:
                # Check if user is admin
                is_admin = user_data.get('privileges') == 'admin'
                return True, is_admin
        return False, None
    except Exception as e:
        print(f"Error checking credentials: {str(e)}")
        return False, None

def find_json_maps_in_downloads():
    """Recherche tous les fichiers JSON dans le dossier Downloads"""
    downloads_folder = os.path.join(os.path.expanduser('~'), 'Downloads')
    json_files = glob.glob(os.path.join(downloads_folder, "*.json"))
    
    maps_data = {}
    for json_file in json_files:
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                # Vérifier si c'est un fichier de carte (contient des coordonnées)
                for key, value in data.items():
                    if isinstance(value, list) and len(value) > 0:
                        if isinstance(value[0], dict) and 'lat' in value[0] and 'lng' in value[0]:
                            maps_data[key] = value
                            print(f"Trouvé carte '{key}' dans {json_file}")
        except Exception as e:
            print(f"Erreur lors de la lecture de {json_file}: {str(e)}")
    
    return maps_data

class LoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Boat Navigation System - Login")
        self.setGeometry(100, 100, 800, 600)
        self.init_ui()
    
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left side - Image
        image_label = QLabel()
        image_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "image.jpg")
        if os.path.exists(image_path):
            pixmap = QPixmap(image_path)
            pixmap = pixmap.scaled(500, 500, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            image_label.setPixmap(pixmap)
            image_label.setAlignment(Qt.AlignCenter)
        else:
            image_label.setText("Login Image\n(Place image.jpg in the same directory)")
            image_label.setStyleSheet("""
                background-color: #f8f9fa;
                padding: 20px;
                color: #7f8c8d;
                font-size: 14px;
            """)
            image_label.setAlignment(Qt.AlignCenter)
        
        # Right side - Login form
        login_widget = QWidget()
        login_layout = QVBoxLayout(login_widget)
        login_layout.setContentsMargins(40, 40, 40, 40)
        
        # Title
        title_label = QLabel("Boat Navigation System")
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # Form layout
        form_layout = QGridLayout()
        form_layout.setVerticalSpacing(15)
        
        # Username
        username_label = QLabel("Username:")
        username_label.setStyleSheet("font-size: 16px;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter your username")
        self.username_input.setStyleSheet("""
            padding: 10px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            font-size: 16px;
        """)
        
        # Password
        password_label = QLabel("Password:")
        password_label.setStyleSheet("font-size: 16px;")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Enter your password")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            padding: 10px;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            font-size: 16px;
        """)
        
        # Add to form layout
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_input, 0, 1)
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(self.password_input, 1, 1)
        
        # Login button
        login_button = QPushButton("Login")
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 12px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        login_button.clicked.connect(self.handle_login)
        
        # Add widgets to login layout
        login_layout.addWidget(title_label)
        login_layout.addSpacing(20)
        login_layout.addLayout(form_layout)
        login_layout.addSpacing(30)
        login_layout.addWidget(login_button)
        login_layout.addStretch()
        
        # Add widgets to main layout
        main_layout.addWidget(image_label, 1)
        main_layout.addWidget(login_widget, 1)
    
    def handle_login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "Error", "Please enter both username and password")
            return
        
        is_valid, is_admin = check_user_credentials(username, password)
        
        if is_valid:
            if is_admin:
                self.nav_window = MapNavigator()
                self.nav_window.show()
                self.close()
            else:
                QMessageBox.critical(self, "Access Denied", 
                    "Sorry, only administrators can access this application.\n"
                    "Please contact your system administrator for access.")
        else:
            QMessageBox.critical(self, "Error", "Invalid username or password")

class NavigationManager:
    def __init__(self, grid_size=0.5, resolution=0.05):
        self.grid_size = grid_size
        self.resolution = resolution
        self.waypoints = []
        self.map_img = None
        self.height = 0
        self.width = 0
        self.current_map_name = None
    
    def load_map(self, map_path):
        """Load a map from file"""
        try:
            self.map_img = cv2.imread(map_path, cv2.IMREAD_GRAYSCALE)
            if self.map_img is None:
                raise FileNotFoundError(f"Could not load map file: {map_path}")
            self.height, self.width = self.map_img.shape
            self.waypoints = []  # Clear waypoints when loading new map
            return True
        except Exception as e:
            print(f"Error loading map: {str(e)}")
            return False
    
    def add_waypoint(self, x, y):
        try:
            if y < 0 or y >= self.height or x < 0 or x >= self.width:
                return False
            pixel_value = self.map_img[int(y)][int(x)]
            if pixel_value != 255:  # Only white pixels (255) are navigable
                return False
            self.waypoints.append(QPointF(x, y))
            return True
        except Exception as e:
            print(f"Error adding waypoint: {str(e)}")
            return False
    
    def remove_nearest_waypoint(self, x, y):
        if not self.waypoints:
            return
        try:
            distances = [(p.x() - x)**2 + (p.y() - y)**2 for p in self.waypoints]
            if distances:
                nearest_idx = distances.index(min(distances))
                del self.waypoints[nearest_idx]
        except Exception as e:
            print(f"Error removing waypoint: {str(e)}")
    
    def clear_waypoints(self):
        self.waypoints = []
    
    def get_waypoints(self):
        return self.waypoints
    
    def save_waypoints(self, filename="waypoints.yaml"):
        if not self.waypoints:
            return False
        try:
            waypoints_meters = []
            for p in self.waypoints:
                x = p.x() * self.resolution
                y = (self.height - p.y()) * self.resolution
                waypoints_meters.append((x, y))
            with open(filename, "w") as f:
                f.write("# Boat navigation waypoints\n")
                f.write("waypoints:\n")
                for i, (x, y) in enumerate(waypoints_meters):
                    f.write(f"  - point{x:.2f}_{y:.2f}:\n")
                    f.write(f"      x: {x:.2f}\n")
                    f.write(f"      y: {y:.2f}\n")
                    f.write(f"      name: wp{i+1}\n")
            return True
        except Exception as e:
            print(f"Error saving waypoints: {str(e)}")
            return False
    
    def get_waypoints_data(self):
        """Get waypoints data in a format suitable for Firebase"""
        if not self.waypoints:
            return None
        
        try:    
            waypoints_meters = []
            for i, p in enumerate(self.waypoints, 1):  # Start enumeration from 1
                x = p.x() * self.resolution
                y = (self.height - p.y()) * self.resolution
                waypoints_meters.append({
                    "point_number": i,
                    "point_name": f"point{i}",
                    "x": round(x, 2),
                    "y": round(y, 2)
                })
                
            return {
                "map_name": self.current_map_name,
                "timestamp": datetime.now().isoformat(),
                "waypoints": waypoints_meters,
                "resolution": self.resolution,
                "grid_size": self.grid_size
            }
        except Exception as e:
            print(f"Error preparing waypoints data: {str(e)}")
            return None

class CoveragePathPlanner:
    def __init__(self, map_img, grid_size=0.5, resolution=0.05):
        self.map_img = map_img
        self.height, self.width = map_img.shape
        self.grid_size = grid_size
        self.resolution = resolution
    
    def get_navigable_range(self, y):
        """Get the min and max x values that are navigable at a given y"""
        if y < 0 or y >= self.height:
            return None, None
        
        row = self.map_img[int(y)]
        navigable = row == 255
        
        if not np.any(navigable):
            return None, None
        
        # Find contiguous navigable regions
        indices = np.where(navigable)[0]
        if len(indices) == 0:
            return None, None
        
        return indices[0], indices[-1]
    
    def generate_path(self, x0, y0):
        """Generate a coverage path starting from (x0, y0)"""
        # Round starting point to grid
        grid_px = int(self.grid_size / self.resolution)
        x_start = int(x0 / grid_px) * grid_px
        y_start = int(y0 / grid_px) * grid_px
        
        # Ensure starting point is navigable
        if self.map_img[int(y_start)][int(x_start)] != 255:
            # Find nearest navigable point
            for dy in range(-grid_px, grid_px+1, int(grid_px/2)):
                for dx in range(-grid_px, grid_px+1, int(grid_px/2)):
                    y_candidate = y_start + dy
                    x_candidate = x_start + dx
                    if (0 <= y_candidate < self.height and 
                        0 <= x_candidate < self.width and
                        self.map_img[int(y_candidate)][int(x_candidate)] == 255):
                        y_start = y_candidate
                        x_start = x_candidate
                        break
                else:
                    continue
                break
        
        # Generate path
        points = [(x_start, y_start)]
        y_current = y_start
        x_current = x_start
        direction = 1  # 1 = right, -1 = left
        vertical_step = grid_px
        
        while True:
            # Move horizontally
            x_min, x_max = self.get_navigable_range(y_current)
            if x_min is None or x_max is None:
                break
                
            if direction == 1:  # Moving right
                x_target = x_max
                step = grid_px
            else:  # Moving left
                x_target = x_min
                step = -grid_px
            
            # Add points along the horizontal path
            x_candidate = x_current
            while (direction == 1 and x_candidate <= x_target) or (direction == -1 and x_candidate >= x_target):
                x_candidate += step
                if (direction == 1 and x_candidate <= x_target) or (direction == -1 and x_candidate >= x_target):
                    if 0 <= x_candidate < self.width and self.map_img[int(y_current)][int(x_candidate)] == 255:
                        points.append((x_candidate, y_current))
                        x_current = x_candidate
            # Move up
            y_next = y_current - vertical_step
            if y_next < 0:
                break
            # Check if there's navigable area at y_next
            x_min_next, x_max_next = self.get_navigable_range(y_next)
            if x_min_next is None or x_max_next is None:
                break
            points.append((x_current, y_next))
            y_current = y_next
            direction = -direction
        return points

class PathVisualizer:
    def __init__(self, scene, nav_manager):
        self.scene = scene
        self.nav_manager = nav_manager
        self.waypoint_items = []
        self.path_items = []
        self.grid_items = []
    
    def clear_all(self):
        """Clear all visualization items"""
        try:
            for item in self.waypoint_items + self.path_items + self.grid_items:
                self.scene.removeItem(item)
            self.waypoint_items = []
            self.path_items = []
            self.grid_items = []
        except Exception as e:
            print(f"Error clearing visualization: {str(e)}")
    
    def update_display(self):
        """Update the display with current waypoints and path"""
        try:
            # Clear existing waypoints and path
            for item in self.waypoint_items + self.path_items:
                self.scene.removeItem(item)
            self.waypoint_items = []
            self.path_items = []
            
            # Draw waypoints and path
            self.draw_waypoints()
            self.draw_path()
        except Exception as e:
            print(f"Error updating display: {str(e)}")
    
    def draw_grid(self):
        """Draw grid on the map"""
        try:
            for item in self.grid_items:
                self.scene.removeItem(item)
            self.grid_items.clear()
            
            pen = QPen(QColor(100, 100, 255, 100))
            pen.setWidth(1)
            
            grid_spacing_px = int(self.nav_manager.grid_size / self.nav_manager.resolution)
            
            for x in range(0, self.nav_manager.width, grid_spacing_px):
                line = self.scene.addLine(x, 0, x, self.nav_manager.height, pen)
                self.grid_items.append(line)
            
            for y in range(0, self.nav_manager.height, grid_spacing_px):
                line = self.scene.addLine(0, y, self.nav_manager.width, y, pen)
                self.grid_items.append(line)
        except Exception as e:
            print(f"Error drawing grid: {str(e)}")
    
    def draw_waypoints(self):
        """Draw waypoints on the map"""
        waypoints = self.nav_manager.get_waypoints()
        if not waypoints:
            return
        
        try:
            for i, point in enumerate(waypoints):
                # Draw point
                ellipse = self.scene.addEllipse(point.x()-5, point.y()-5, 10, 10, 
                                               QPen(QColor(255, 0, 0)), 
                                               QColor(255, 0, 0, 150))
                self.waypoint_items.append(ellipse)
                
                # Draw label
                text = self.scene.addText(str(i+1))
                text.setPos(point.x()+5, point.y()-20)
                text.setDefaultTextColor(Qt.black)
                self.waypoint_items.append(text)
        except Exception as e:
            print(f"Error drawing waypoints: {str(e)}")
    
    def draw_path(self):
        """Draw path between waypoints"""
        waypoints = self.nav_manager.get_waypoints()
        if len(waypoints) < 2:
            return
        
        try:
            pen = QPen(QColor(0, 255, 0))  # Green lines for path
            pen.setWidth(2)
            
            for i in range(len(waypoints) - 1):
                p1 = waypoints[i]
                p2 = waypoints[i+1]
                line = self.scene.addLine(p1.x(), p1.y(), p2.x(), p2.y(), pen)
                self.path_items.append(line)
                
                # Calculate distance
                dx = p2.x() - p1.x()
                dy = p2.y() - p1.y()
                dist_px = math.sqrt(dx**2 + dy**2)
                dist_m = dist_px * self.nav_manager.resolution
                
                # Add distance label
                mid_x = (p1.x() + p2.x()) / 2
                mid_y = (p1.y() + p2.y()) / 2
                text = self.scene.addText(f"{dist_m:.2f} m")
                text.setPos(mid_x, mid_y - 10)
                text.setDefaultTextColor(Qt.black)
                self.path_items.append(text)
        except Exception as e:
            print(f"Error drawing path: {str(e)}")

class CustomGraphicsView(QGraphicsView):
    def __init__(self, scene):
        super().__init__(scene)
        self.setMouseTracking(True)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.nav_manager = None  # Will be set by MapNavigator
    
    def set_nav_manager(self, nav_manager):
        """Set the navigation manager for coordinate conversion"""
        self.nav_manager = nav_manager
    
    def mouseMoveEvent(self, event):
        """Handle mouse movement and update coordinate display"""
        super().mouseMoveEvent(event)
        if self.nav_manager and self.nav_manager.map_img is not None:
            # Get mouse position in scene coordinates
            pos = self.mapToScene(event.pos())
            x, y = pos.x(), pos.y()
            
            # Convert to real-world coordinates (meters)
            if 0 <= x < self.nav_manager.width and 0 <= y < self.nav_manager.height:
                real_x = x * self.nav_manager.resolution
                real_y = (self.nav_manager.height - y) * self.nav_manager.resolution
                
                # Update status label with coordinates
                navigator = self.parent().parent()
                if hasattr(navigator, 'update_coordinate_status'):
                    navigator.update_coordinate_status(real_x, real_y)
    
    def wheelEvent(self, event: QWheelEvent):
        navigator = self.parent().parent()
        if hasattr(navigator, 'zoom_in') and hasattr(navigator, 'zoom_out'):
            if event.angleDelta().y() > 0:
                navigator.zoom_in()
            else:
                navigator.zoom_out()
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton or event.button() == Qt.RightButton:
            navigator = self.parent().parent()
            pos = self.mapToScene(event.pos())
            x, y = pos.x(), pos.y()
            if event.button() == Qt.RightButton and hasattr(navigator, 'remove_nearest_waypoint'):
                navigator.remove_nearest_waypoint(x, y)
            elif hasattr(navigator, 'add_waypoint'):
                navigator.add_waypoint(x, y)
        else:
            super().mousePressEvent(event)

class MapGeneratorThread(QThread):
    progress_signal = pyqtSignal(int, str)
    finished_signal = pyqtSignal(str, bool)
    
    def __init__(self, coordinates, output_path, resolution):
        super().__init__()
        self.coordinates = coordinates
        self.output_path = output_path
        self.resolution = resolution
    
    def run(self):
        try:
            self.progress_signal.emit(10, "Initializing map generation...")
            
            # Validate coordinates
            is_valid, error_message = validate_coordinates(self.coordinates)
            if not is_valid:
                self.finished_signal.emit(f"Error: {error_message}", False)
                return
            
            self.progress_signal.emit(20, "Converting coordinates...")
            
            # Generate the map
            result = generate_map(
                self.coordinates, 
                output_path=self.output_path, 
                resolution=self.resolution,
                progress_callback=lambda p, m: self.progress_signal.emit(p, m)
            )
            
            if result:
                self.finished_signal.emit("Map generated successfully", True)
            else:
                self.finished_signal.emit("Failed to generate map", False)
        except Exception as e:
            self.finished_signal.emit(f"Error: {str(e)}", False)

class MapNavigator(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Boat Navigation Waypoint Planner")
        self.setGeometry(100, 100, 1000, 800)
        
        # Create maps directory if it doesn't exist
        self.maps_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "maps")
        os.makedirs(self.maps_dir, exist_ok=True)
        
        self.nav_manager = NavigationManager(grid_size=0.5, resolution=0.05)
        self.current_map_name = None
        self.current_map_path = None
        
        self.zoom_level = 1.0
        self.zoom_factor = 1.25
        self.min_zoom = 0.1
        self.max_zoom = 10.0
        
        # Flag to track if mission has been started
        self.mission_started = False
        
        # Initialize upload button
        self.upload_btn = QPushButton("Upload to Firebase")
        self.upload_btn.clicked.connect(self.upload_waypoints)
        self.upload_btn.setEnabled(False)  # Initially disabled
        
        # Add Start Mission button
        self.start_mission_btn = QPushButton("Start Mission")
        self.start_mission_btn.clicked.connect(self.start_mission)
        self.start_mission_btn.setEnabled(False)  # Initially disabled
        
        # Add End Mission button
        self.end_mission_btn = QPushButton("End Mission")
        self.end_mission_btn.clicked.connect(self.end_mission)
        self.end_mission_btn.setEnabled(False)  # Initially disabled
        
        # Configurer un observateur de fichiers pour le dossier Downloads
        self.downloads_folder = os.path.join(os.path.expanduser('~'), 'Downloads')
        self.file_watcher = QFileSystemWatcher([self.downloads_folder])
        self.file_watcher.directoryChanged.connect(self.on_downloads_changed)
        
        # Timer pour éviter les rafraîchissements trop fréquents
        self.refresh_timer = QTimer()
        self.refresh_timer.setSingleShot(True)
        self.refresh_timer.timeout.connect(self.refresh_maps)
        
        # Status label for coordinate display
        self.status_label = QLabel("Ready")
        
        self.init_ui()
    
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # Create two control layouts for better organization
        top_control_layout = QHBoxLayout()
        bottom_control_layout = QHBoxLayout()
        
        # Add map selection dropdown
        self.map_selector = QComboBox()
        self.map_selector.addItems(get_available_maps())
        self.map_selector.currentIndexChanged.connect(self.on_map_selected)
        
        # Add generate map button
        self.generate_map_btn = QPushButton("Generate Map")
        self.generate_map_btn.clicked.connect(self.generate_selected_map)
        
        # Add upload waypoints button (next to generate map)
        self.upload_btn = QPushButton("Upload Waypoints")
        self.upload_btn.clicked.connect(self.upload_waypoints)
        self.upload_btn.setEnabled(False)  # Initially disabled
        
        # Add Start Mission button
        self.start_mission_btn = QPushButton("Start Mission")
        self.start_mission_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-size: 16px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:pressed {
                background-color: #219a52;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
                cursor: not-allowed;
            }
        """)
        self.start_mission_btn.setCursor(Qt.PointingHandCursor)
        self.start_mission_btn.clicked.connect(self.start_mission)
        self.start_mission_btn.setEnabled(False)  # Initially disabled

        # Add End Mission button
        self.end_mission_btn = QPushButton("End Mission")
        self.end_mission_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-size: 16px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
                cursor: not-allowed;
            }
        """)
        self.end_mission_btn.setCursor(Qt.PointingHandCursor)
        self.end_mission_btn.clicked.connect(self.end_mission)
        self.end_mission_btn.setEnabled(False)  # Initially disabled
        
        # Add buttons to top control layout
        top_control_layout.addWidget(self.map_selector)
        top_control_layout.addWidget(self.generate_map_btn)
        top_control_layout.addWidget(self.upload_btn)
        top_control_layout.addStretch()  # Add stretch to push mission buttons to the right
        top_control_layout.addWidget(self.start_mission_btn)
        top_control_layout.addWidget(self.end_mission_btn)
        
        self.scene = QGraphicsScene()
        self.view = CustomGraphicsView(self.scene)
        self.view.setMouseTracking(True)
        self.view.setRenderHint(QPainter.Antialiasing)
        self.view.setDragMode(QGraphicsView.ScrollHandDrag)
        self.view.set_nav_manager(self.nav_manager)  # Set nav_manager for coordinate conversion
        
        self.path_visualizer = PathVisualizer(self.scene, self.nav_manager)
        
        # Initialize empty scene
        self.setup_empty_scene()
        
        self.zoom_in_btn = QPushButton("Zoom In (+)")
        self.zoom_in_btn.clicked.connect(self.zoom_in)
        
        self.zoom_out_btn = QPushButton("Zoom Out (-)")
        self.zoom_out_btn.clicked.connect(self.zoom_out)
        
        self.reset_zoom_btn = QPushButton("Reset Zoom")
        self.reset_zoom_btn.clicked.connect(self.reset_zoom)
        
        self.clear_btn = QPushButton("Clear Waypoints")
        self.clear_btn.clicked.connect(self.clear_waypoints)
        
        self.save_btn = QPushButton("Save Waypoints")
        self.save_btn.clicked.connect(self.save_waypoints)
        
        self.create_path_btn = QPushButton("Create Path Planning")
        self.create_path_btn.clicked.connect(self.create_coverage_path)
        
        # Add buttons to bottom control layout
        bottom_control_layout.addWidget(self.zoom_in_btn)
        bottom_control_layout.addWidget(self.zoom_out_btn)
        bottom_control_layout.addWidget(self.reset_zoom_btn)
        bottom_control_layout.addWidget(self.clear_btn)
        bottom_control_layout.addWidget(self.save_btn)
        bottom_control_layout.addWidget(self.create_path_btn)
        
        # Disable buttons until map is loaded
        self.set_buttons_enabled(False)
        
        # Add both control layouts to main layout
        main_layout.addLayout(top_control_layout)
        main_layout.addLayout(bottom_control_layout)
        
        main_layout.addWidget(self.view)
        main_layout.addWidget(self.status_label)
        
        self.view.centerOn(self.scene.sceneRect().center())
    
    def setup_empty_scene(self):
        """Setup an empty scene with a message"""
        try:
            self.scene.clear()
            self.path_visualizer.clear_all()
            # Add a text item to indicate no map is loaded
            text = self.scene.addText("No map loaded.\nPlease select a map and click 'Generate Map'")
            text.setDefaultTextColor(Qt.gray)
            # Center the text
            text.setPos(self.view.width()/2 - text.boundingRect().width()/2,
                       self.view.height()/2 - text.boundingRect().height()/2)
        except Exception as e:
            print(f"Error setting up empty scene: {str(e)}")
            self.status_label.setText(f"Error setting up empty scene: {str(e)}")
    
    def set_buttons_enabled(self, enabled):
        """Enable or disable buttons based on map loading state"""
        try:
            self.zoom_in_btn.setEnabled(enabled)
            self.zoom_out_btn.setEnabled(enabled)
            self.reset_zoom_btn.setEnabled(enabled)
            self.clear_btn.setEnabled(enabled)
            self.save_btn.setEnabled(enabled)
            self.create_path_btn.setEnabled(enabled)
            self.upload_btn.setEnabled(enabled)
            self.start_mission_btn.setEnabled(enabled and not self.mission_started)
            self.end_mission_btn.setEnabled(enabled and self.mission_started)
        except Exception as e:
            print(f"Error setting button states: {str(e)}")
    
    def on_map_selected(self, _):
        """Handle map selection from dropdown"""
        self.current_map_name = self.map_selector.currentText()
        self.status_label.setText(f"Selected map: {self.current_map_name}. Click 'Generate Map' to create it.")
    
    def upload_waypoints(self):
        """Upload waypoints to Firebase"""
        try:
            # Get waypoints data
            waypoints_data = self.nav_manager.get_waypoints_data()
            
            if not waypoints_data:
                QMessageBox.warning(self, "No Waypoints", 
                                  "Please add some waypoints before uploading.")
                return
            
            # Print data that would be uploaded (for testing)
            print("Preparing to upload waypoints data:")
            print(json.dumps(waypoints_data, indent=2))
            
            # Show confirmation dialog
            reply = QMessageBox.question(self, 'Confirm Upload',
                                       f'Upload {len(waypoints_data["waypoints"])} waypoints to Firebase?',
                                       QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                # Upload to Firebase
                try:
                    initialize_firebase()
                    upload_waypoints_to_firebase(waypoints_data)
                    QMessageBox.information(self, "Success", 
                                          "Waypoints uploaded successfully!")
                except Exception as e:
                    QMessageBox.critical(self, "Upload Error", 
                                       f"Failed to upload waypoints: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "Error", 
                               f"An error occurred while preparing waypoints: {str(e)}")
    
    def generate_selected_map(self):
        """Generate map for the selected area with caching"""
        if not self.current_map_name:
            self.status_label.setText("Please select a map first")
            return
        
        # Generate map filename based on map name
        map_filename = f"{self.current_map_name.replace(' ', '_')}.pgm"
        self.current_map_path = os.path.join(self.maps_dir, map_filename)
        
        # Check if the map already exists and is recent
        if os.path.exists(self.current_map_path):
            map_mtime = os.path.getmtime(self.current_map_path)
            map_age = time.time() - map_mtime
            
            # If map is less than 1 hour old, use it
            if map_age < 3600:
                self.status_label.setText(f"Using cached map for {self.current_map_name}")
                
                # Load the existing map
                if self.nav_manager.load_map(self.current_map_path):
                    self.nav_manager.current_map_name = self.current_map_name
                    # Create a new scene
                    self.scene = QGraphicsScene()
                    self.view.setScene(self.scene)
                    self.path_visualizer = PathVisualizer(self.scene, self.nav_manager)
                    self.view.set_nav_manager(self.nav_manager)
                    self.setup_scene()
                    self.set_buttons_enabled(True)
                    self.status_label.setText(f"Loaded cached map: {self.current_map_name}")
                    return
        
        # Continue with normal map generation if no cache or cache is old
        # Get the path to the JSON file containing the map coordinates
        map_file_path = get_map_file_path(self.current_map_name)
        
        if not map_file_path:
            # Si le fichier n'est pas trouvé, chercher dans Downloads
            downloads_folder = os.path.join(os.path.expanduser('~'), 'Downloads')
            json_files = glob.glob(os.path.join(downloads_folder, "*.json"))
            
            for json_file in json_files:
                try:
                    with open(json_file, 'r') as f:
                        data = json.load(f)
                        if self.current_map_name in data:
                            map_file_path = json_file
                            break
                except:
                    continue
        
        if not map_file_path:
            self.status_label.setText(f"Could not find JSON file for map: {self.current_map_name}")
            return
        
        try:
            # Load coordinates from the JSON file
            with open(map_file_path, 'r') as f:
                map_data = json.load(f)
                coordinates = map_data.get(self.current_map_name, [])
        
            if not coordinates:
                self.status_label.setText(f"No coordinates found for map: {self.current_map_name}")
                return
        
            # Calculer la taille approximative de la carte
            lats = [point['lat'] for point in coordinates]
            lngs = [point['lng'] for point in coordinates]
        
            # Estimation grossière de la taille en mètres
            lat_range = max(lats) - min(lats)
            lng_range = max(lngs) - min(lngs)
        
            # Conversion approximative en mètres (1 degré ≈ 111 km à l'équateur)
            width_m = lng_range * 111000 * math.cos(math.radians(sum(lats)/len(lats)))
            height_m = lat_range * 111000
        
            # Ajuster la résolution en fonction de la taille
            if width_m > 5000 or height_m > 5000:
                resolution = 0.5  # 50cm par pixel pour les très grandes cartes
            elif width_m > 1000 or height_m > 1000:
                resolution = 0.2  # 20cm par pixel pour les grandes cartes
            else:
                resolution = 0.1  # 10cm par pixel pour les cartes normales
        
            self.status_label.setText(f"Generating map with resolution {resolution}m/pixel...")
        
            # Disable all buttons during map generation
            self.set_buttons_enabled(False)
        
            # Generate map filename based on map name
            map_filename = f"{self.current_map_name.replace(' ', '_')}.pgm"
            self.current_map_path = os.path.join(self.maps_dir, map_filename)
        
            # Create progress dialog
            self.progress_dialog = QProgressDialog("Generating map...", "Cancel", 0, 100, self)
            self.progress_dialog.setWindowTitle("Map Generation")
            self.progress_dialog.setWindowModality(Qt.WindowModal)
            self.progress_dialog.setMinimumDuration(0)
            self.progress_dialog.setValue(0)
            self.progress_dialog.show()
        
            # Create and start the generator thread
            self.generator_thread = MapGeneratorThread(
                coordinates, 
                self.current_map_path, 
                resolution=resolution
            )
        
            # Connect signals
            self.generator_thread.progress_signal.connect(self.update_generation_progress)
            self.generator_thread.finished_signal.connect(self.on_map_generation_finished)
            self.progress_dialog.canceled.connect(self.generator_thread.terminate)
        
            # Start the thread
            self.generator_thread.start()
        
        except Exception as e:
            self.status_label.setText(f"Error preparing map generation: {str(e)}")
            self.set_buttons_enabled(True)

    def update_generation_progress(self, value, message):
        """Update the progress dialog"""
        if hasattr(self, 'progress_dialog') and self.progress_dialog.isVisible():
            self.progress_dialog.setValue(value)
            self.progress_dialog.setLabelText(message)

    def on_map_generation_finished(self, message, success):
        """Handle map generation completion"""
        # Close the progress dialog
        if hasattr(self, 'progress_dialog'):
            self.progress_dialog.close()
        
        if success:
            # Load the generated map
            if self.nav_manager.load_map(self.current_map_path):
                self.nav_manager.current_map_name = self.current_map_name
                # Create a new scene
                self.scene = QGraphicsScene()
                self.view.setScene(self.scene)
                self.path_visualizer = PathVisualizer(self.scene, self.nav_manager)
                self.view.set_nav_manager(self.nav_manager)
                self.setup_scene()
                self.set_buttons_enabled(True)
                self.status_label.setText(f"Generated and loaded map: {self.current_map_name}")
            else:
                self.status_label.setText("Error loading generated map")
                self.set_buttons_enabled(True)
        else:
            self.status_label.setText(message)
            self.set_buttons_enabled(True)
    
    def setup_scene(self):
        """Setup the scene with the current map"""
        try:
            if self.nav_manager.map_img is None:
                self.setup_empty_scene()
                return
            
            # Create new map display
            map_display = cv2.cvtColor(self.nav_manager.map_img, cv2.COLOR_GRAY2RGB)
            bytes_per_line = 3 * self.nav_manager.width
            qimg = QImage(map_display.data, self.nav_manager.width, self.nav_manager.height, 
                         bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(qimg)
            self.scene.addPixmap(pixmap)
            
            # Add border visualization
            border_mask = (self.nav_manager.map_img == 0).astype(np.uint8) * 255
            border_image = np.zeros((self.nav_manager.height, self.nav_manager.width, 4), dtype=np.uint8)
            border_image[:, :, 2] = border_mask  # Red channel
            border_image[:, :, 3] = border_mask  # Alpha channel
            bytes_per_line = 4 * self.nav_manager.width
            qimg_border = QImage(border_image.tobytes(), self.nav_manager.width, self.nav_manager.height, 
                                bytes_per_line, QImage.Format_ARGB32)
            pixmap_border = QPixmap.fromImage(qimg_border)
            self.scene.addPixmap(pixmap_border)
            
            # Draw new grid and update display
            self.path_visualizer.draw_grid()
            self.path_visualizer.update_display()
            
            # Reset zoom and center view
            self.zoom_level = 1.0
            self.view.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)
        except Exception as e:
            print(f"Error setting up scene: {str(e)}")
            self.status_label.setText(f"Error setting up scene: {str(e)}")
    
    def add_waypoint(self, x, y):
        if self.nav_manager.add_waypoint(x, y):
            self.path_visualizer.update_display()
            self.update_status()
        else:
            self.status_label.setText("Cannot place waypoint on border or non-navigable area!")
    
    def remove_nearest_waypoint(self, x, y):
        self.nav_manager.remove_nearest_waypoint(x, y)
        self.path_visualizer.update_display()
        self.update_status()
    
    def clear_waypoints(self):
        self.nav_manager.clear_waypoints()
        self.path_visualizer.update_display()
        self.update_status()
    
    def save_waypoints(self):
        self.nav_manager.save_waypoints()
        self.status_label.setText(f"Saved {len(self.nav_manager.get_waypoints())} waypoints to waypoints.yaml")
    
    def create_coverage_path(self):
        if not self.nav_manager.get_waypoints():
            self.status_label.setText("Please add at least one waypoint as starting point")
            return
        start_point = self.nav_manager.get_waypoints()[0]
        x0, y0 = start_point.x(), start_point.y()
        planner = CoveragePathPlanner(self.nav_manager.map_img)
        coverage_points = planner.generate_path(x0, y0)
        for p in coverage_points:
            self.nav_manager.add_waypoint(p[0], p[1])
        self.path_visualizer.update_display()
        self.update_status()
    
    def update_status(self):
        wp_count = len(self.nav_manager.get_waypoints())
        self.status_label.setText(f"{wp_count} waypoints | "
                                f"Zoom: {self.zoom_level:.1f}x | "
                                "Click to add waypoint, Right-click to remove")
    
    def zoom_in(self):
        self.zoom_level = min(self.zoom_level * self.zoom_factor, self.max_zoom)
        self.apply_zoom()
    
    def zoom_out(self):
        self.zoom_level = max(self.zoom_level / self.zoom_factor, self.min_zoom)
        self.apply_zoom()
    
    def reset_zoom(self):
        self.zoom_level = 1.0
        self.apply_zoom()
        self.view.fitInView(self.scene.sceneRect(), Qt.KeepAspectRatio)
    
    def apply_zoom(self):
        transform = self.view.transform()
        transform.reset()
        transform.scale(self.zoom_level, self.zoom_level)
        self.view.setTransform(transform)
    
    def update_coordinate_status(self, x, y):
        """Update the status label with current cursor coordinates"""
        wp_count = len(self.nav_manager.get_waypoints())
        # Convert to real-world coordinates
        real_x = x * self.nav_manager.resolution
        real_y = (self.nav_manager.height - y) * self.nav_manager.resolution
        
        self.status_label.setText(
            f"Pixel Coordinates: X: {int(x)}, Y: {int(y)} | "
            f"Real-world: X: {real_x:.2f}m, Y: {real_y:.2f}m | "
            f"{wp_count} waypoints | "
            f"Zoom: {self.zoom_level:.1f}x | "
            "Click to add waypoint, Right-click to remove"
        )
    
    def start_mission(self):
        """Execute the ROS launch file for the mission"""
        try:
            # Disable the start button and enable end button
            self.mission_started = True
            self.start_mission_btn.setEnabled(False)
            self.end_mission_btn.setEnabled(True)
            self.start_mission_btn.setText("Mission Started")
            
            # Command to execute the ROS launch file
            launch_command = "cd ~/asv_ws && roslaunch asv_wave_sim_gazebo ocean_world.launch"
            
            # Execute the command
            process = subprocess.Popen(
                launch_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for a short time to check if the command started successfully
            try:
                _, stderr = process.communicate(timeout=5)
                if process.returncode != 0:
                    raise Exception(f"Command failed with error: {stderr.decode()}")
            except subprocess.TimeoutExpired:
                # If the command is still running after timeout, it's probably successful
                pass
            
            QMessageBox.information(self, "Success", "Mission started successfully!")
            
        except Exception as e:
            self.mission_started = False
            self.start_mission_btn.setEnabled(True)
            self.end_mission_btn.setEnabled(False)
            self.start_mission_btn.setText("Start Mission")
            
            error_msg = str(e)
            QMessageBox.critical(
                self,
                "Error Starting Mission",
                f"Failed to start mission: {error_msg}\n\n"
                "Please check if ROS is running and try again manually."
            )

    def end_mission(self):
        """End the current mission by killing the ROS processes"""
        try:
            # Disable end button and enable start button
            self.mission_started = False
            self.start_mission_btn.setEnabled(True)
            self.end_mission_btn.setEnabled(False)
            self.start_mission_btn.setText("Start Mission")
            
            # Command to kill ROS processes
            kill_command = "pkill -f 'roslaunch asv_wave_sim_gazebo'"
            
            # Execute the command
            process = subprocess.Popen(
                kill_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # Wait for the command to complete
            _, stderr = process.communicate()
            
            if process.returncode != 0 and stderr:
                raise Exception(f"Command failed with error: {stderr.decode()}")
            
            QMessageBox.information(self, "Success", "Mission ended successfully!")
            
        except Exception as e:
            error_msg = str(e)
            QMessageBox.critical(
                self,
                "Error Ending Mission",
                f"Failed to end mission: {error_msg}\n\n"
                "Please try to end the mission manually by closing the Gazebo window."
            )

    def on_downloads_changed(self, _):
        """Appelé quand le contenu du dossier Downloads change"""
        # Attendre 2 secondes avant de rafraîchir pour éviter les rafraîchissements multiples
        self.refresh_timer.start(2000)

    def refresh_maps(self):
        """Rafraîchir la liste des cartes disponibles"""
        new_maps = get_available_maps()
        current_maps = self.map_selector.currentText()

        # Get current items in the combo box
        current_items = [self.map_selector.itemText(i) for i in range(self.map_selector.count())]

        if new_maps != current_items:
            self.map_selector.clear()
            self.map_selector.addItems(new_maps)

            if current_maps in new_maps:
                self.map_selector.setCurrentText(current_maps)
            else:
                if new_maps:  # Check if there are any maps available
                    self.map_selector.setCurrentIndex(0)
                self.status_label.setText("Selected map has been removed. Please select a new map.")

        self.set_buttons_enabled(False)  # Désactiver les boutons jusqu'à ce qu'une carte soit sélectionnée

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    sys.exit(app.exec_())
